<?xml version="1.0" encoding="UTF-8" ?>
<web-app xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://java.sun.com/xml/ns/javaee" xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-app_3_0.xsd" version="3.0">
    <display-name>admin</display-name>


    <servlet>
        <servlet-name>TreeServlet</servlet-name>
        <servlet-class>com.tmax.anylink.tree.TreeServlet</servlet-class>
    </servlet>
    <servlet>
        <servlet-name>FormDataServlet</servlet-name>
        <servlet-class>com.tmax.anylink.formdata.FormDataServlet</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>TableDataServlet</servlet-name>
        <servlet-class>com.tmax.anylink.table.TableDataServlet</servlet-class>
    </servlet>
    <servlet>
        <display-name>Login</display-name>
        <servlet-name>Login</servlet-name>
        <servlet-class>com.tmax.anylink.login.Login</servlet-class>
    </servlet>
    <servlet>
        <display-name>SSOLogin</display-name>
        <servlet-name>SSOLogin</servlet-name>
        <servlet-class>com.tmax.anylink.login.SSOLogin</servlet-class>
    </servlet>
    <servlet>
        <display-name>Logout</display-name>
        <servlet-name>Logout</servlet-name>
        <servlet-class>com.tmax.anylink.login.Logout</servlet-class>
    </servlet>
    <servlet>
        <display-name>ChangePassword</display-name>
        <servlet-name>ChangePassword</servlet-name>
        <servlet-class>com.tmax.anylink.login.ChangePassword</servlet-class>
    </servlet>
    <servlet>
        <display-name>UploadServlet</display-name>
        <servlet-name>UploadServlet</servlet-name>
        <servlet-class>com.tmax.anylink.file.UploadServlet</servlet-class>
                <multipart-config>
            <max-file-size>1048576000</max-file-size>
            <max-request-size>2097152000</max-request-size>
            <file-size-threshold>524288000</file-size-threshold>
        </multipart-config>
    </servlet>
    <servlet>
        <display-name>UploadServlet_studio</display-name>
        <servlet-name>UploadServlet_studio</servlet-name>
        <servlet-class>com.tmax.anylink.file.UploadServlet_studio</servlet-class>
                <multipart-config>
            <max-file-size>1048576000</max-file-size>
            <max-request-size>2097152000</max-request-size>
            <file-size-threshold>524288000</file-size-threshold>
        </multipart-config>
    </servlet>
    <servlet>
        <display-name>DownloadServlet</display-name>
        <servlet-name>DownloadServlet</servlet-name>
        <servlet-class>com.tmax.anylink.file.DownloadServlet</servlet-class>
    </servlet>
    <servlet>
		<servlet-name>jsp64k</servlet-name>
		<servlet-class>jeus.servlet.servlets.JspServlet</servlet-class>
		<init-param>
			<param-name>genStrAsCharArray</param-name>
			<param-value>true</param-value>
		</init-param>
	</servlet>
	<servlet-mapping>
		<servlet-name>jsp64k</servlet-name>
		<url-pattern>*.jsp</url-pattern>
	</servlet-mapping>
    <servlet-mapping>
        <servlet-name>DownloadServlet</servlet-name>
        <url-pattern>/DownloadServlet</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>UploadServlet</servlet-name>
        <url-pattern>/admin/config/UploadServlet</url-pattern>
        <url-pattern>/admin/admin/UploadServlet</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>Login</servlet-name>
        <url-pattern>/login</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>SSOLogin</servlet-name>
        <url-pattern>/SSOlogin</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>Logout</servlet-name>
        <url-pattern>/Logout</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>TableDataServlet</servlet-name>
        <url-pattern>/table</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>TreeServlet</servlet-name>
        <url-pattern>/tree</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>FormDataServlet</servlet-name>
        <url-pattern>/form</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>UploadServlet</servlet-name>
        <url-pattern>/up</url-pattern>
    </servlet-mapping>


    <welcome-file-list>
        <welcome-file>/index.jsp</welcome-file>
    </welcome-file-list>
    <listener>
        <listener-class>com.tmax.anylink.login.SessionListener</listener-class>
    </listener>
    <filter>
        <description>로그인 필터</description>
        <filter-name>Loginfilter</filter-name>
        <filter-class>com.tmax.anylink.login.Loginfilter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>Loginfilter</filter-name>
        <url-pattern>*.jsp</url-pattern>
        <url-pattern>*.js</url-pattern>
        <url-pattern>/index</url-pattern>
        <url-pattern>/render/*</url-pattern>
        <url-pattern>/tree</url-pattern>
        <url-pattern>/table</url-pattern>
    </filter-mapping>
      <filter>
        <description>공통 필터</description>
        <filter-name>Commonfilter</filter-name>
        <filter-class>com.tmax.anylink.common.CommonFilter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>Commonfilter</filter-name>
        <url-pattern>/form</url-pattern>
    </filter-mapping>

    <filter>
        <filter-name>i18nFilter</filter-name>
        <filter-class>com.tmax.anylink.i18n.I18nFilter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>i18nFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>
    <filter>
        <filter-name>ForwardFilter</filter-name>
        <filter-class>com.tmax.anylink.ForwardFilter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>ForwardFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>
    
    <filter>
        <filter-name>AuthFilter</filter-name>
        <filter-class>com.tmax.anylink.auth.AuthFilter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>AuthFilter</filter-name>
        <url-pattern>*.jsp</url-pattern>
    </filter-mapping>
    
    <servlet>
        <display-name>messages</display-name>
        <servlet-name>messages</servlet-name>
        <servlet-class>com.tmax.anylink.i18n.MessagesServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>messages</servlet-name>
        <url-pattern>/messages</url-pattern>
    </servlet-mapping>
    <servlet>
        <display-name>angularjs sample</display-name>
        <servlet-name>render</servlet-name>
        <servlet-class>com.tmax.anylink.JadeTemplateServlet</servlet-class>
        <load-on-startup>1</load-on-startup>
    </servlet>
    <servlet-mapping>
        <servlet-name>render</servlet-name>
        <url-pattern>/render/*</url-pattern>
    </servlet-mapping>
    <servlet>
        <display-name>page</display-name>
        <servlet-name>page</servlet-name>
        <servlet-class>com.tmax.anylink.JadeTemplateServlet</servlet-class>
        <load-on-startup>1</load-on-startup>
        <init-param>
            <param-name>base</param-name>
            <param-value>/WEB-INF/pages</param-value>
        </init-param>
    </servlet>
    <servlet-mapping>
        <servlet-name>page</servlet-name>
        <url-pattern>/page/*</url-pattern>
    </servlet-mapping>
	<security-constraint>
		<web-resource-collection>
			<web-resource-name></web-resource-name>
			<url-pattern>/*</url-pattern>
			<http-method>TRACE</http-method>
			<http-method>PUT</http-method>
			<http-method>DELETE</http-method>
			<http-method>CONNECT</http-method>
			<http-method>OPTIONS</http-method>
			<http-method>HEAD</http-method>
		</web-resource-collection>
		<auth-constraint>
			<role-name></role-name>
		</auth-constraint>
	</security-constraint>
	
	<error-page>
		<error-code>403</error-code>
		<location>/error.jsp</location>
	</error-page>
	<error-page>
		<error-code>404</error-code>
		<location>/error.jsp</location>
	</error-page>
	<error-page>
		<exception-type>java.lang.Throwable</exception-type>
		<location>/error.jsp</location>
	</error-page>
	<error-page>
		<error-code>500</error-code>
		<location>/error.jsp</location>
	</error-page>



</web-app>
