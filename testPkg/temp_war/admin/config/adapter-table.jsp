<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<script type="text/javascript" src="../../js/jquery-1.11.0.js"></script>
<script type="text/javascript" src="../../js/jquery-ui.js"></script>
<link rel="stylesheet" href="../../css/slick.grid.css" type="text/css"/>

<link rel="stylesheet" href="../../css/bootstrap.css">
<link rel="stylesheet" href="../../css/common2.css" type="text/css"/>
<script type="text/javascript" src="../../js/sessAlert.js"></script>


<script>
<% String user=(String)session.getAttribute("id"); %>
var username="<%=user%>";




function moveFocus(next){
	
	if(event.keyCode == 13){
		document.getElementById(next).focus();
	}

}

var bizsystemId = '';
var adapter = '';

$(document).ready(function() {
	initSessionMonitor();
	$("#btn_excel").click(function(){
		startajax_excel_export();
		JSONToCSVConvertor(Adapter_Data, "Adapter_List", true);
		Adapter_Data = [];
	});



	
	$(".layer_popbox").draggable({
		handle: ".popup_title" , containment : "window"});
	$( ".layer_popbox,.popup_title" ).disableSelection();

	
	$(".layer_popbox2").draggable({
		handle: ".popup_title2" , containment : "window"
		});
	$( ".layer_popbox2,.popup_title2" ).disableSelection();
	var queries = {};

	$.each(document.location.search.substr(1).split('&'),function(c,q){

		if(q != '')
		{
			var i = q.split('=');
			queries[i[0].toString()] = i[1].toString();
		}
	});


	if(queries.bizsystemId != undefined)
	{
		bizsystemId = queries.bizsystemId;
	}
	if(queries.adapter != undefined)
	{
		adapter = queries.adapter;
	}
	bizsysListCall();

	function bizsysListCall(){
		$.ajax({ 
			url:'<%=request.getContextPath()%>/form?create=bizsyslist',
			type:'get',
			dataType: 'json',
			cache:false,
			contentType: "charset=UTF-8",
			success:function(data){

				var leng = data.length;
				for(var j = 0; j < leng; j++)
				{
					if(bizsystemId == data[j].id)
						$("#bizsyslist").append('<option id="' + data[j].id + '"val="' + data[j].id + '" selected="selected">' + data[j].name + '</option>');
					else
				    $("#bizsyslist").append('<option id="' + data[j].id + '"val="' + data[j].id + '" >' + data[j].name + '</option>');
				}
				if(adapter != '')
				{
					$("#adapterprotocol").val(adapter).attr("selected", "selected");
				}
				searchajax(1);
			},
			error:function(a,b,c){

			}

		});
	}
});



function JSONToCSVConvertor(JSONData, ReportTitle, ShowLabel) {


    
    var arrData = typeof JSONData != 'object' ? JSON.parse(JSONData) : JSONData;

    var CSV = '';
    
    
    
    if (ShowLabel) {
        var row = "";
        
        for (var index in arrData[0]) {
            
            row += index + ',';
        }

        row = row.slice(0, -1);
        
        CSV += row + '\r\n';
    }

    
    for (var i = 0; i < arrData.length; i++) {
        var row = "";
        
        for (var index in arrData[i]) {
            row += '"' + arrData[i][index] + '",';
        }

        row.slice(0, row.length - 1);
        
        CSV += row + '\r\n';
    }

    if (CSV == '') {
        alert("Invalid data");
        return;
    }


    var blob = new Blob([decodeURIComponent('%ef%bb%bf')+CSV],{type: "text/csv;charset=utf-8;"}); 
    if (navigator.msSaveBlob) { 
       navigator.msSaveBlob(blob, "Adapter_List.csv")
    } else {
      var pom = document.createElement('a');
      pom.setAttribute('href', 'data:text/csv;charset=utf-8,%EF%BB%BF' + encodeURIComponent((CSV)));
      pom.setAttribute('download', 'Adapter_List.csv');
      pom.click();

    }
}

var Adapter_Data = [];
function startajax_excel_export(){ 
				
			var searchobj = new Object();
			if (searchobj.queryWrapperDTO == undefined) {
			searchobj.queryWrapperDTO = new Object();
			}


			
				var criteria = [];


            adtidquery = '';
            adtnamequery = '';
            
            if ($('#search_opt :selected').val() == 'search_adapterid') {
                if ($("#adtnamesearch").val() != '' && $("#adtnamesearch").val() != 'undefined')
                    adtidquery = $("#adtnamesearch").val();
            } else if ($('#search_opt :selected').val() == 'search_adaptername') {
                if ($("#adtnamesearch").val() != '' && $("#adtnamesearch").val() != 'undefined')
                    adtnamequery = $("#adtnamesearch").val();
            } else if($('#search_opt :selected').val() == 'search_adapteridname') {
                if ($("#adtnamesearch").val() != '' && $("#adtnamesearch").val() != 'undefined') {
                    adtidquery = $("#adtnamesearch").val();
                    adtnamequery = $("#adtnamesearch").val();
                }
            }

			var bizsystemidsearch='';
            
            
			if($('#bizsyslist option:selected').attr('id')!='defaultoption'){

				bizsystemidsearch = $('#bizsyslist option:selected').attr('id');

					criteria.push({
							"key" : "bizsystemId", 
							"value" : bizsystemidsearch 
					});
					searchobj.queryWrapperDTO.criteria = criteria;

			}
				
			if($('#adapterprotocol :selected').val()!='${messages["common.all"]}'){
				adapterprotocol = $('#adapterprotocol :selected').val();

				
			}else{
				adapterprotocol = ''; 
			}




			var searchquery = JSON.stringify(searchobj);
      $.ajax({
	          url:'<%=request.getContextPath()%>/table?ConfigAdapter=adaptertable',
	          type:'get',
						async: false,
						data:{
		  				searchquery : searchquery,
		  				adapterprotocol : adapterprotocol,
		  				bizsystemidsearch : bizsystemidsearch,
                        adtidquery: adtidquery,
		  				adtnamequery: adtnamequery
		  		},
	          dataType: 'json',
	          contentType: "charset=UTF-8",
	          success:function(data){

	              var slickData = [];

	                  for ( var i = 0 ; i < data.length ; i ++) {
	                      slickData[i] = data[i].adapterTableDTO;
	                  }
	                  for ( var i = 0 ; i < slickData.length ; i ++) {
						var userId ="",protocol="",name="",version="",modificationDate="",registeringUser="",bizsystemName="";
	                    if(slickData[i].bizsystemId != undefined){
	                      userId = slickData[i].bizsystemId;
	                    }
	                    if(slickData[i].bizsystemName != undefined){
		                  bizsystemName = slickData[i].bizsystemName;
		                }
	                    if(slickData[i].protocol != undefined){
	                      protocol = slickData[i].protocol;
	                    }
	                    if(slickData[i].name != undefined){
	                      name = slickData[i].name;
	                    }
	                    if(slickData[i].version != undefined){
	                      version = slickData[i].version;
	                    }
	                    if(slickData[i].modificationDate != undefined){
	                      modificationDate = slickData[i].modificationDate;
	                    }
	                    if(slickData[i].registeringUser != undefined){
	                      registeringUser = slickData[i].registeringUser;
	                    }
	    	  	  				Adapter_Data.push({
	                       "${messages['common.bizsystem.name']}" : bizsystemName,
	                       "${messages['common.adapter.type']}" : protocol,
	                       "${messages['common.adapter.name']}" :name,
	                       "${messages['common.version']}" :version,
	                       "${messages['common.final.mod.date']}" : " "+modificationDate,
	                       "${messages['admin.user.createUser']}" :registeringUser,
	                    });
	    	  	  			}

	                  return Adapter_Data;

	          },
	          error:function(err){
	          }

	      });
  }




</script>

</head>
<body>
    <div id="wrapper">
        <div id="contents">
        <jsp:include page="../endpoint-startstop.jsp" flush="false"/>
            <div class="contents_top">
                <div class="contents_bar">
                    <ul class="location">
                        <li class="current">${messages['common.all']}</li>
                    </ul>
                    <ul class="list_btn btn_r20 mg_t8">
                        <li><a href="adapter-table.jsp"><button class="sbtn_list" style="font-weight: bold; color:black;"
                            id="btn_clusterlist">${messages['common.adapter.list']}</button></a></li>
                        <li><a href="endpointgroup-table.jsp"><button class="sbtn_list"
                            id="btn_clusterlist">${messages['common.epg.list']}</button></a></li>
                        <li><a href="endpoint-table.jsp"><button class="sbtn_list"
                            id="btn_serverlist">${messages['common.ep.list']}</button></a></li>
                    </ul>
                </div>
            </div>
            
            <div id="contents_body" class="contents_body">
				<div class="title">${messages['common.adapter.list']}</div>

                
                <ul class="search_box mg_b5">
					<li>
						<div class="search_tit">${messages['common.bizsystem.name']}</div>

						<div class="styled-select">
								<select id="bizsyslist" onkeydown="moveFocus('adapterprotocol')">
															<option id="defaultoption">${messages["common.all"]}</option>
								</select>
							</div>
					</li>
					<li>
						<div class="search_tit" >${messages['common.adapter.type']}</div>
						<div class="styled-select">
							<select id="adapterprotocol" onkeydown="moveFocus('adtnamesearch')">
														<option id="defaultprotocol">${messages["common.all"]}</option>
														<option value="TCP">TCP</option>
														<option value="HTTP">HTTP</option>
														<option value="TMAX">Tmax</option>
														<option value="WEB_SERVICE">WebService</option>
														<option value="DB">DB</option>
														<option value="FILE">FILE</option>
														<option value="LOG">Log</option>
														<option value="FTP">FTP</option>
														<option value="MQ">MQ</option>
														<option value="SAP">SAP</option>
														<option value="UDP">UDP</option>
														<option value="SMTP">SMTP</option>
														<option value="WEBDAV">WebDAV</option>
														<option value="TUXEDO">Tuxedo</option>
														<option value="JMS">JMS</option>
														<option value="EB_XML">ebXML</option>
														<option value="ISO_8583">ISO8583</option>
														<option value="PRO_OBJECT">ProObject</option>
														<option value="PRO_FRAME">ProFrame</option>
														<option value="KAFKA">Kafka</option>
                                                        <option value="FIX">FIX</option>
							</select>
						</div>
					</li>
                    <li>
                        <div class="search_tit" > </div>
                        <div class="styled-select" id ="search_opt">
                            <select>
                                <option id = "search_adapterid" value = "search_adapterid">${messages['common.adapter.id']}</option>
                                <option id = "search_adaptername" value = "search_adaptername">${messages['common.adapter.name']}</option>
                                <option id = "search_adapteridname" value = "search_adapteridname" selected>${messages['common.adapter.idname']}</option>
                            </select>
                        </div>
						<div class="input-box">
							<input type="text" id="adtnamesearch" onkeydown="moveFocus('searchbtn')"/>
						</div>
					</li>
                    <li>
						<button class="big_search" id="searchbtn">${messages['common.search']}</button>
					</li>
                </ul>
				
				<div class="table">
					<div class="table">
						<div id="adapterGrid"></div>
					</div>
				</div>
                <div class="al_c_2 ">
                    <div id="btn_bottom_list" class="btn_l">
						<button class="sbtn_add" id="btn_add">${messages['config.system.system-table.Add']}</button>
                        <button class="sbtn_delete" id="btn_del">Del</button>
                        <button class="sbtn_start" id="all_start_btn">Start</button>
                   		<button class="sbtn_stop" id="all_stop_btn">Stop</button>
                   		<button class="sbtn_download" id="btn_excel" alt="ly1">Export</button>
                    </div>
                    <div class="btn_r" id="page_max_count">
                    	<div class="gridviewoption_left">${messages['common.list.size.per.page']}</div>
                        <input type="text" id="gridviewoption" value="10" onkeypress="return gridviewchange(event)"/>
												<button class="pageCount" data-page="10">10</button>
												<button class="pageCount" data-page="25">25</button>
												<button class="pageCount" data-page="50">50</button>
												<button class="pageCount" data-page="100">100</button>
												<button class="pageCount" data-page="500">500</button>
                    </div>
                    <div class="pagbtn">
                        <ul id='paging'></ul>
                    </div>
                </div>
            </div>

        </div>
    </div>


<script src="../../js/lib/jquery.event.drag-2.2.js"></script>
<script src="../../js/slick.core.js"></script>
<script src="../../js/plugins/slick.checkboxselectcolumn.js"></script>
<script src="../../js/plugins/slick.autotooltips.js"></script>
<script src="../../js/plugins/slick.cellrangedecorator.js"></script>
<script src="../../js/plugins/slick.cellrangeselector.js"></script>
<script src="../../js/plugins/slick.cellcopymanager.js"></script>
<script src="../../js/plugins/slick.rowselectionmodel.js"></script>
<script src="../../js/slick.formatters.js"></script>
<script src="../../js/slick.editors.js"></script>
<script src="../../js/slick.grid.js"></script>
<script src="../../js/slick.customize.js"></script>

<script src="../../js/bootstrapv3.js"></script>
<script src="../../js/bootstrap-paginator.js"></script>
<script src="../../js/resize-table-buttons.js"></script>

<script>
  var myObj = new Object();
  var grid;
  var data = [];
  var options = {

  };
  var columns = [];
  var checkboxSelector;

  $(function(){

    var element = $('#paging');

    var options_bootstrap = {
        size:"small",
        bootstrapMajorVersion:3,
        currentPage: pageNo,
        numberOfPages: 10,
        totalPages: totalpage,
        alignment:'center',
        onPageClicked: function(e,originalEvent,type,page){

            searchajax(page);
        },
        itemTexts:function(type, page,current){
         switch (type) {
            case "first":
                return "◀◀";
            case "prev":
                return "◀";
            case "next":
                return "▶";
            case "last":
                return "▶▶";
            case "page":
                return page;

            }
	   }
    };

    element.bootstrapPaginator(options_bootstrap);

});


  $(function () {

    checkboxSelector = new Slick.CheckboxSelectColumn({
      cssClass: "slick-cell-checkboxsel"
    });

    columns.push(checkboxSelector.getColumnDefinition());
    columns.push(
          {id: "${messages['config.system.system-table.table.no']}", name: "${messages['admin.user.no']}", field: "${messages['config.system.system-table.table.no']}",width: 50,resizable: false,cssClass: "cell-effort-flow", sortable: false},
          {id: "bizsystemName", name: "${messages['common.bizsystem.name']}", field: "bizsystemName",width: 190, sortable: false},
          {id: "protocol", name: "${messages['common.adapter.type']}", field: "protocol",width: 200, sortable: true},
          {id: "name", name: "${messages['common.adapter.name']}", field: "name",width: 200, sortable: true},
          {id: "version", name: "${messages['common.version']}", field: "version",cssClass: "cell-effort-flow",width: 85,sortable: false},
          {id: "modificationDate", name: "${messages['common.final.mod.date']}", field: "modificationDate",width: 250, sortable: false},
          {id: "registeringUser", name: "${messages['admin.user.createUser']}", field: "registeringUser",width: 140, sortable: false}
          );

  });

  var viewoptionNo;
  if(localStorage.viewoptionNoADT == undefined){
  	viewoptionNo = 10;
  }else{
  	viewoptionNo = localStorage.viewoptionNoADT;
  	$("#gridviewoption").val(viewoptionNo);
  }
  function gridviewchange(e){
  	if(e.keyCode == 13){
  		if($("#gridviewoption").val() > 1000){
  			alert("${messages['config.adapter.page.size.setting.under.1000']}")
  		}else{
  			viewoptionNo = $("#gridviewoption").val();
  	  	    localStorage.viewoptionNoADT = viewoptionNo;
  	  		$('#paging').bootstrapPaginator( {currentPage: 1});
  	  	    searchajax(1);
  		}

  	}
  }

  function deleteTableCell(sysIdList, bizsystemIdList){

	  	  	$.ajax({
	  	  		url:'<%=request.getContextPath()%>/table?ConfigAdapter=deleteAdapterTable&sysIdList='+sysIdList+'&bizsystemIdList='+bizsystemIdList,
				cache: false,
	  	  		type:'get',
	  	  		async: false,
	  	  		dataType: 'text',
	  	  		contentType: "charset=UTF-8",
	  	  		success:function(data){
	  	  			if(data =='SUCCESS'){
	  	  			    asyncAlert("${messages['userinfo.delete.success']}", function(){
	  	  				top.document.location.reload();});
	  	  			}else if(data =='Fail'){
	  	  			    asyncAlert("${messages['common.delete.fail.tryagain']}");

	  	  			}else if(data =='EXISTCHILDSUCCESS'){
	  	  			    asyncAlert("${messages['common.adapter.exist.child.fail']}");
	  	  			}else if(data =='MSDOWNSUCCESS'){
	  	  			    asyncAlert("${messages['common.check.rte']}");
	  	  			}else if(data =='AuthFail'){
	  	  			    asyncAlert("${messages['common.auth.fail']}", function(){
		                top.document.location.reload();});
              		}else if(data =='EXISTCHILD'){
              		    asyncAlert("${messages['common.adapter.exist.child.fail']}");
	  				}else{
	  				    asyncAlert(data);
              }
	  			},
	  	  		error:function(err){
	  	  		}

	  	  	});
  }
  var adapterprotocol;
  var adtidquery;
  var adtnamequery;
  $(document).ready(function(){

	 $(".pageCount").click(function(e) {
			const columnCount = e.currentTarget.dataset.page
			$("#gridviewoption").val(columnCount);
			viewoptionNo = $("#gridviewoption").val();
			localStorage.viewoptionNoADT = viewoptionNo;
			$('#paging').bootstrapPaginator( {currentPage: 1});
			searchajax(1);
		})

	  $("#btn_del").click(function(){
		  var selectedrows = [];

		  var sysIdList = [];
      var bizsystemIdList = [];
		  selectedrows = grid.getSelectedRows();
		  var item = grid.getDataItem(selectedrows);
		  if(selectedrows == undefined||selectedrows==''){
	  			 alert("${messages['config.adapter.select.adapter.to.delete']}");
	  	  }else{
	  		  	var delete_flag = true;

	  			for(var i=0; i<selectedrows.length; i++){
	  				  item = grid.getDataItem(selectedrows[i]);
	  				  if(item.sysId == "default-log-adapter"){
	  				  	delete_flag = false;
	  				  }
	  			}

	  		  	if(delete_flag){
	  		  		if(confirm("${messages['common.confirm.delete']}") == true){    

	  			  		for(var i=0; i<selectedrows.length; i++){
	  				  		item = grid.getDataItem(selectedrows[i]);
	  				  		sysIdList[i] = item.sysId;
                  bizsystemIdList[i] =item.bizsystemId;

	  			 		}
	  			  		deleteTableCell(sysIdList, bizsystemIdList);
	        		}else{   
	        	    	return;
	        		}
	  		  	}else{
	  		  		alert("${messages['config.adapter.default-log-adt.del.fail']}");
	  		  	}

	  	  }

	  });


 	  $("#searchbtn").click(function(){
	  $('#paging').bootstrapPaginator( {currentPage: 1});
        searchajax(1);


  	 });
		 $("#btn_add").click(function(){
			if (adapter == "TMAX")
				adapter = "Tmax";
			else if (adapter == "WEB_SERVICE")
				adapter = "WebService";
			else if (adapter == "EB_XML")
				adapter = "ebXML";
			else if (adapter == "PRO_OBJECT")
				adapter = "ProObject";
			else if (adapter == "ISO_8583")
				adapter = "ISO8583";
			else if (adapter == "KAFKA")
        adapter = "Kafka";

			 location.assign("create-adapter.jsp?bizsystemId="+bizsystemId+"&adapter="+adapter);
		});
  });

	$(document).ready(function() {
		$("#eppopup").draggable({
            handle : ".popup_title",
            containment : "window"
        });

		$("#eppopup_x_btn").click(function() {
            $("#eppopup").hide();
			top.document.getElementById("treearea").contentWindow.getParentNodeId("", "");
        });
		$("#eppopup_cancel_btn").click(function() {
            $("#eppopup").hide();
			top.document.getElementById("treearea").contentWindow.getParentNodeId("", "");
        });
	});

var pageNo;
var totalpage;
var columnId2;
var asc2;

function searchajax(pageNo) {
	
    var page = pageNo - 1;
    var searchobj = new Object();
    if (searchobj.queryWrapperDTO == undefined) {
    	searchobj.queryWrapperDTO = new Object();
    }

    searchobj.queryWrapperDTO.pageSize = viewoptionNo;
    searchobj.queryWrapperDTO.pageNumber = page;

    
    
    adtidquery = '';
    adtnamequery = '';
    
    if ($('#search_opt :selected').val() == 'search_adapterid') {
        if ($("#adtnamesearch").val() != '' && $("#adtnamesearch").val() != 'undefined')
            adtidquery = $("#adtnamesearch").val();
    } else if ($('#search_opt :selected').val() == 'search_adaptername') {
        if ($("#adtnamesearch").val() != '' && $("#adtnamesearch").val() != 'undefined')
            adtnamequery = $("#adtnamesearch").val();
    } else if($('#search_opt :selected').val() == 'search_adapteridname') {
        if ($("#adtnamesearch").val() != '' && $("#adtnamesearch").val() != 'undefined') {
            adtidquery = $("#adtnamesearch").val();
            adtnamequery = $("#adtnamesearch").val();
        }
    }

    
    var criteria = [];
    var bizsystemidsearch = '';
    if ($('#bizsyslist option:selected').attr('id') != 'defaultoption') {
    	bizsystemidsearch = $('#bizsyslist option:selected').attr('id');
    	criteria.push({
    		"key" : "bizsystemId", 
    		"value" : bizsystemidsearch 
		});
        searchobj.queryWrapperDTO.criteria = criteria;
    }

    
    if ($('#adapterprotocol :selected').val() != '${messages["common.all"]}')
    	adapterprotocol = $('#adapterprotocol :selected').val();
    else
    	adapterprotocol = ''; 

	var searchquery = JSON.stringify(searchobj);

    startajax(searchquery, adapterprotocol, bizsystemidsearch, adtidquery, adtnamequery, columnId2, asc2);
}

var clickCnt = 0;
function startajax(searchquery, adapterprotocol, bizsystemidsearch, adtidquery, adtnamequery, columnId, asc) {
  	$.ajax({
  		url: '<%=request.getContextPath()%>/table?ConfigAdapter=adaptertable',
  		type: 'get',
  		data: {
  			searchquery : searchquery,
  			adapterprotocol : adapterprotocol,
  			bizsystemidsearch : bizsystemidsearch,
            adtidquery: adtidquery,
  			adtnamequery: adtnamequery,
  			columnId : columnId,
  			asc : asc
  		},
  		dataType: 'json',
  		contentType: "charset=UTF-8",
  		success: function(data) {
  			var slickData = [];

 	  	  	for (var i = 0; i < data.length; i ++)
 	  	  		slickData[i] = data[i].adapterTableDTO;
          	myObj = slickData;


			if(grid == '' || grid == undefined)
            {
            	grid = new Slick.Grid("#adapterGrid", slickData, columns, options);                         
                grid.autosizeColumns();
            }else{
                grid = new Slick.Grid("#adapterGrid", slickData, grid.getColumns(), options);            
            }
  		    grid.setSelectionModel(new Slick.RowSelectionModel({selectActiveRow: false}));
  		    grid.registerPlugin(checkboxSelector);
  		    grid.registerPlugin(new Slick.AutoTooltips({enableForHeaderCells: true}));
           	grid.render();
 
           	grid.getSelectionModel().setSelectedRanges([]);

           	if(data != null && data != '') {
           		var totalpagevalue = data[0].adapterTableDTO.maxCount;
           		totalpage = Math.ceil(totalpagevalue/viewoptionNo);
           		$('#paging').bootstrapPaginator( {totalPages: totalpage});
           	} else {
           		totalpage = 1;
           		$('#paging').bootstrapPaginator( {totalPages: totalpage});
           	}

 	 	  	
 	 	  	grid.onClick.subscribe(function(e, args) {

            	var item = args.grid.getData()[args.row];
            	var cell = args.cell;
            	var id = item.id;
            	var bizsystemId = item.bizsystemId;

            	var protocol = item.protocol;
	 	      	if (cell != 0) {
	 	      		if (protocol != "Log"){
	 	      			location.replace("common-adapter-info.jsp?adapterInfo=read&id="+id+"&bizsystemId="+bizsystemId);
	 	      		}else{
	 	      			location.replace("log-adapter-info.jsp?adapterInfo=read&id="+id+"&bizsystemId="+bizsystemId);
	 	      		}
	 	      		top.document.getElementById("treearea").contentWindow.getParentNodeId(id, bizsystemId);
	 	      	}
 	 	    });
  		  	grid.onSort.subscribe(function(e, args) {
            	clickCnt += 1;
            	var sortasc;
            	if (clickCnt%2 == 0){
            		sortasc = false;
            	}else{
            		sortasc = true;
            	}
            	columnId2 = args.sortCol.id;
            	asc2 = sortasc;
            	if(columnId2 == "protocol" || columnId2 == "name"){
            		startajax(searchquery, adapterprotocol, bizsystemidsearch, adtidquery, adtnamequery, columnId2, asc2);
            	}

  		  	});
  		  grid.setSortColumn(columnId2, asc2);
  		},
  		error: function(a, b, c) {
  		}
  	});

	
	
};



function toJavascript(data){
    var array=data;
	    array=array.replace("[", "");
	    array=array.replace("]", "");
    return array.split(",");

}
function endpointstatechagne(sysIdList,state,bizsystemIdList){ 
	var resourceType = "Adapter";
	  	$.ajax({
	  		url:'<%=request.getContextPath()%>/table?ConfigAdapter=statechagelist',
			    cache: false,
	  		type:'post',
	  		async: true,
	  		dataType: 'text',
	  		data: 'resourceType='+resourceType+'&sysIdList='+sysIdList+'&state='+state+'&username='+username+'&bizsystemIdList='+bizsystemIdList,
	  		success:function(data){
	  			if(data =='Fail'){
	  			    asyncAlert("${messages['common.status.not.changed']}");
	  			}else if(data =='AuthFail'){
	  			    asyncAlert("${messages['common.auth.fail']}");

		        }else if(data == ''){
		            asyncAlert("${messages['no.results.found']}")
		        }else{
	        		var str = data;
  	          		str = str.replace(/\[SYSID\]/gi,"SysID : ");
  					str = str.replace(/\[SUCCESSSTOP\]/gi, "${messages['config.adapter.endpoint.list.stop']}");
  					str = str.replace(/\[SUCCESSSTART\]/gi, "${messages['config.adapter.endpoint.list.start']}");
  					str = str.replace(/\[FAILSTOP\]/gi, "${messages['config.adapter.endpoint.list.stop.fail']}");
  					str = str.replace(/\[FAILSTART\]/gi, "${messages['config.adapter.endpoint.list.start.fail']}");
  					str = str.replace(/\[RESULT\]/gi, "${messages['config.adapter.endpoint.list.fail.message']}");
					var array = toJavascript(str);
					var slickData = [];
					for(var i=0;i<array.length;i++){
						if (array[i] == "default-log-adapter") {
							slickData[i] = {"${messages['config.adapter.endpoint.list.result']}" : "${messages['config.adapter.endpoint.list.log.cannot.control']}"};
						} else {
							slickData[i] = {"${messages['config.adapter.endpoint.list.result']}" : array[i]};
						}
					}
					var grid; 
                    if(grid == '' || grid == undefined)
                    {
                          grid = new Slick.Grid("#eppopupGrid", slickData, eppopupcolumns, eppopupoptions)                         
                          grid.autosizeColumns();
                    }else{
                          grid = new Slick.Grid("#eppopupGrid", slickData, eppopupcolumns, eppopupoptions)
                                 
                    }

					grid.registerPlugin(new Slick.AutoTooltips({enableForHeaderCells: true}));
					grid.autosizeColumns();
					$("#eppopup").show();
		          	}
			},
	  		error:function(err){
	  		}

	  	});

}


$(document).ready(function() {

  	
  	$('.slick-cell').mouseenter(function () {
  	     $(this.parentNode.children).addClass('slick-cell-hovered') ;

  	});

  	$('.slick-cell').mouseleave(function () {
  	     $(this.parentNode.children).removeClass('slick-cell-hovered');
  	});


	 $("#all_start_btn").click(function(){
			  var selectedrows = [];

			  var sysIdList = [];
	      	  var bizsystemIdList = [];
			  selectedrows = grid.getSelectedRows();
			  var item = grid.getDataItem(selectedrows);
			  if(selectedrows == undefined||selectedrows==''){
		  			 alert("${messages['config.adapter.select.adt.to.change.status']}");
		  	  }else{

		  			for(var i=0; i<selectedrows.length; i++){
  				  		item = grid.getDataItem(selectedrows[i]);
  				  		sysIdList[i] = item.sysId;
              			bizsystemIdList[i] =item.bizsystemId;

  			 		}

		  		  		if(confirm("${messages['config.adapter.confirm.ep.start']}${messages['config.adapter.ep.status.notice']}") == true){    
		  		  			endpointstatechagne(sysIdList,"start",bizsystemIdList);

		        		}else{   
		        	    	return;
		        		}

		  	  }

		  });

	      $("#all_stop_btn").click(function(){
			  var selectedrows = [];

			  var sysIdList = [];
	      	  var bizsystemIdList = [];
			  selectedrows = grid.getSelectedRows();
			  var item = grid.getDataItem(selectedrows);
			  if(selectedrows == undefined||selectedrows==''){
		  			 alert("${messages['config.adapter.select.adt.to.change.status']}");
		  	  }else{

		  			for(var i=0; i<selectedrows.length; i++){
  				  		item = grid.getDataItem(selectedrows[i]);
  				  		sysIdList[i] = item.sysId;
              			bizsystemIdList[i] =item.bizsystemId;

  			 		}

		  		  		if(confirm("${messages['config.adapter.confirm.ep.terminate']}${messages['config.adapter.ep.status.notice']}") == true){    
		  		  			endpointstatechagne(sysIdList,"stop",bizsystemIdList);

		        		}else{   
		        	    	return;
		        		}

		  	  }

		  });
});

</script>
</body>
</html>
